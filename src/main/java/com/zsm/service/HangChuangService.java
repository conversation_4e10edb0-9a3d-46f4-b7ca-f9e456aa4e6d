package com.zsm.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsm.common.SaasAuthorizationVerifyAspect;
import com.zsm.common.exception.BusinessException;
import com.zsm.config.HangChuangConfig;
import com.zsm.constant.DrugDictAccountConstant;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.constant.SyncAccountEnum;
import com.zsm.entity.Nhsa3505;
import com.zsm.entity.YsfStoTcTask;
import com.zsm.entity.YsfStoTcTaskSub;
import com.zsm.mapper.YsfStoTcTaskMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.domain.DateRange;
import com.zsm.model.dto.HisDrugCatalogResponse;
import com.zsm.model.dto.InpatientPrescriptionQueryDto;
import com.zsm.model.dto.OutpatientPrescriptionQueryDto;
import com.zsm.model.enums.SdDpsEnum;
import com.zsm.model.enums.TaskStatusEnum;
import com.zsm.model.nhsa.request.fsi5204.Selinfo5204;
import com.zsm.model.nhsa.response.Fsi5204Response;
import com.zsm.model.nhsa.response.NhsaCityResponse;
import com.zsm.model.saas.request.*;
import com.zsm.model.saas.response.*;
import com.zsm.model.vo.*;
import com.zsm.utils.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 太和人医his杭创通用服务类
 *
 * <AUTHOR>
 * @date 2025/6/9 12:15
 */
@Slf4j
@Service
public class HangChuangService {

    @Resource
    private HangChuangConfig hangChuangConfig;

    @Resource
    private YsfStoTcTaskMapper ysfStoTcTaskMapper;

    @Resource
    private Nhsa3505Service nhsa3505Service;
    @Resource
    private YsfStoTcTaskSubService ysfStoTcTaskSubService;
    @Resource
    private DataPersistenceService dataPersistenceService;
    @Resource
    private TokenCacheService tokenCacheService;
    @Resource
    private SignNoCacheService signNoCacheService;
    @Resource
    private NhsaRetryUtil nhsaRetryUtil;

    /**
     * 门诊处方查询
     * 参考阜阳人医门诊处方查询业务流程，使用HttpRequestUtil调用接口
     * 流程：根据cardType类型确定查询方式：
     * 1-直接使用patientId查询；
     * 2-身份证号查询（先获取患者ID，再查询）；
     * 3-卡号查询（先获取患者ID，再查询）；
     * 4-处方号(cfxh)查询（直接使用cfxh参数查询）
     *
     * @param queryDto 查询dto
     * @return {@link ApiResult }<{@link List }<{@link OutpatientPrescriptionResponseVo.PrescriptionItem }>>
     */
    public ApiResult<List<OutpatientPrescriptionResponseVo.PrescriptionItem>> queryOutpatientPrescription(OutpatientPrescriptionQueryDto queryDto) {
        try {
            log.info("开始查询门诊处方发药数据，查询参数：{}", JSONUtil.toJsonStr(queryDto));

            // 参数校验
            if (queryDto == null) {
                queryDto = new OutpatientPrescriptionQueryDto();
            }

            // 验证cardType和对应参数
            validateQueryParams(queryDto);


            // Step 2: 构建门诊发药明细查询参数
            Map<String, Object> requestParams = buildOutpatientRequestParams(queryDto, null);

            // Step 3: 获取门诊发药明细接口URL
            String requestUrl = hangChuangConfig.getOutpatientDispenseDetailUrl();

            // Step 4: 发起HTTP请求查询门诊发药明细
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "查询门诊发药明细");

            // Step 5: 解析响应结果
            List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList = parseOutpatientResponse(responseBody);

            log.info("his接口查询门诊发药明细成功，返回{}条数据", resultList.size());

            // Step 6: 处理追溯码库存信息（参考阜阳人医逻辑）
            SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
            if (userInfo != null && !resultList.isEmpty()) {
                handleTracCodgStoreForOutpatient(resultList, userInfo.getAuthorization());
            } else {
                log.warn("未获取到SaaS用户信息，跳过追溯码库存处理");
            }

            // Step 7: 处理任务状态和数据过滤（参考阜阳人医门诊处方查询逻辑）
            filterByTaskStatusForOutpatient(resultList);

            // Step 8: 异步保存到nhsa_3505表
            if (!resultList.isEmpty()) {
                nhsa3505Service.save3505Async(resultList);
            }

            log.info("查询门诊处方发药数据完成，最终返回记录数：{}", resultList.size());
            return ApiResult.success(resultList);

        } catch (Exception e) {
            log.error("查询门诊处方发药数据异常", e);
            return ApiResult.error("查询门诊处方发药数据失败：" + e.getMessage());
        }
    }

    public ApiResult<List<InPatientDispenseRecordVo>> queryInpatientDispenseRecord(InpatientPrescriptionQueryDto queryDto) {
        try {
            log.info("开始查询住院发药记录数据，查询参数：{}", JSONUtil.toJsonStr(queryDto));

            // 参数校验
            if (queryDto == null) {
                queryDto = new InpatientPrescriptionQueryDto();
            }

            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams(queryDto);

            // 获取请求URL
            String requestUrl = hangChuangConfig.getInpatientDispenseRecordUrl();

            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "查询住院发药记录");

            // 解析响应结果
            List<InPatientDispenseRecordVo> resultList = HttpRequestUtil.parseResponseToList(responseBody, InPatientDispenseRecordVo.class);

            log.info("查询住院发药记录数据完成，最终返回记录数：{}", resultList.size());
            return ApiResult.success(resultList);

        } catch (Exception e) {
            log.error("查询住院发药记录数据异常", e);
            return ApiResult.error("查询住院发药记录数据失败：" + e.getMessage());
        }
    }

    /**
     * 查询住院处方发药
     * 参考阜阳人医门诊处方查询业务流程，包含任务状态处理和数据过滤
     *
     * @param queryDto 查询dto
     * @return {@link ApiResult }<{@link List }<{@link InPatientDispenseDetailBindScatteredVo }>>
     */
    public ApiResult<List<InPatientDispenseDetailBindScatteredVo>> queryInpatientPrescription(InpatientPrescriptionQueryDto queryDto) {

        try {
            log.info("开始查询住院处方发药数据，查询参数：{}", JSONUtil.toJsonStr(queryDto));

            // 参数校验
            if (queryDto == null) {
                queryDto = new InpatientPrescriptionQueryDto();
            }

            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams(queryDto);

            // 获取请求URL
            String requestUrl = hangChuangConfig.getInpatientDispenseDetailUrl();

            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "查询住院处方发药");

            // 解析响应结果
            List<InPatientDispenseDetailVo> originalList = HttpRequestUtil.parseResponseToList(responseBody, InPatientDispenseDetailVo.class);

            log.info("杭创接口查询成功，返回{}条数据", originalList.size());

            // 转换为扩展VO并处理追溯码库存信息
            List<InPatientDispenseDetailBindScatteredVo> resultList = convertAndHandleTracCodgStore(originalList);

            // 过滤出院带药标志为1的数据
            if (StrUtil.isNotBlank(queryDto.getPatInHosId())) {
                resultList = resultList.stream()
                        .filter(item -> "1".equals(item.getDscgTkdrugFlag()))
                        .collect(Collectors.toList());
            } else {
                resultList = resultList.stream()
                        .filter(item -> "0".equals(item.getDscgTkdrugFlag()))
                        .collect(Collectors.toList());
            }

            // 处理追溯码库存信息（需要SaaS用户信息）
            SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
            if (userInfo != null) {
                handleTracCodgStoreForInpatient(resultList, userInfo.getAuthorization());
            } else {
                log.warn("未获取到SaaS用户信息，跳过追溯码库存处理");
            }

            // 处理任务状态和数据过滤（参考阜阳人医门诊处方查询逻辑）
            filterByTaskStatus(resultList, queryDto);

            // 同步保存到nhsa_3505表
            if (!resultList.isEmpty()) {
                nhsa3505Service.saveInpatientDataToNhsa3505(resultList);
                log.info("使用同步方法保存住院发药数据到3505表");
            }

            log.info("查询住院处方发药数据完成，最终返回记录数：{}", resultList.size());
            return ApiResult.success(resultList);

        } catch (Exception e) {
            log.error("查询住院处方发药数据异常", e);
            return ApiResult.error("查询住院处方发药数据失败：" + e.getMessage());
        }
    }

    /**
     * 转换为扩展VO并处理追溯码库存信息
     * 参考阜阳人医的handleTracCodgStoreCydy方法逻辑
     *
     * @param originalList 原始住院发药明细列表
     * @return 扩展的住院发药明细列表（包含拆零信息）
     */
    private List<InPatientDispenseDetailBindScatteredVo> convertAndHandleTracCodgStore(List<InPatientDispenseDetailVo> originalList) {
        if (originalList == null || originalList.isEmpty()) {
            return new ArrayList<>();
        }

        List<InPatientDispenseDetailBindScatteredVo> extendedList = new ArrayList<>();

        // 转换数据类型
        for (InPatientDispenseDetailVo original : originalList) {
            InPatientDispenseDetailBindScatteredVo extended = new InPatientDispenseDetailBindScatteredVo();
            // 复制基础字段
            copyBasicFields(original, extended);
            extendedList.add(extended);
        }


        return extendedList;
    }

    /**
     * 复制基础字段从原始VO到扩展VO
     *
     * @param source 原始VO
     * @param target 目标扩展VO
     */
    private void copyBasicFields(InPatientDispenseDetailVo source, InPatientDispenseDetailBindScatteredVo target) {
        // 复制所有基础字段
        target.setRecordId(source.getRecordId());
        target.setRecordDetailId(source.getRecordDetailId());
        target.setOriDetailId(source.getOriDetailId());
        target.setIdFee(source.getIdFee());
        target.setNaFee(source.getNaFee());
        target.setSdClassify(source.getSdClassify());
        target.setFgDps(source.getFgDps());
        target.setSendFlag(source.getSendFlag());
        target.setSendTime(source.getSendTime());
        target.setRtalDocno(source.getRtalDocno());
        target.setStooutNo(source.getStooutNo());
        target.setPatWardId(source.getPatWardId());
        target.setPatWardName(source.getPatWardName());
        target.setFyyf(source.getFyyf());
        target.setDeptId(source.getDeptId());
        target.setPharCertno(source.getPharCertno());
        target.setPharName(source.getPharName());
        target.setPharPracCertNo(source.getPharPracCertNo());
        target.setSelRetnTime(source.getSelRetnTime());
        target.setHisDrugCode(source.getHisDrugCode());
        target.setMedListCodg(source.getMedListCodg());
        target.setSpec(source.getSpec());
        target.setProdentpName(source.getProdentpName());
        target.setFixmedinsHilistId(source.getFixmedinsHilistId());
        target.setFixmedinsHilistName(source.getFixmedinsHilistName());
        target.setManuLotnum(source.getManuLotnum());
        target.setManuDate(source.getManuDate());
        target.setExpyEnd(source.getExpyEnd());
        target.setBchno(source.getBchno());
        target.setRxFlag(source.getRxFlag());
        target.setTrdnFlag(source.getTrdnFlag());
        target.setHisDosUnit(source.getHisDosUnit());
        target.setHisPacUnit(source.getHisPacUnit());
        target.setHisConRatio(source.getHisConRatio());
        target.setFixmedinsBchno(source.getFixmedinsBchno());
        target.setPatInHosId(source.getPatInHosId());
        target.setMdtrtSn(source.getMdtrtSn());
        target.setPsnNo(source.getPsnNo());
        target.setPsnName(source.getPsnName());
        target.setBedNo(source.getBedNo());
        target.setMdtrtSetlType(source.getMdtrtSetlType());
        target.setOrderId(source.getOrderId());
        target.setPrscDrCertno(source.getPrscDrCertno());
        target.setPrscDrName(source.getPrscDrName());
        target.setSelRetnCnt(source.getSelRetnCnt());
        target.setSelRetnUnit(source.getSelRetnUnit());
        target.setCfxh(source.getCfxh());
        target.setCfmxxh(source.getCfmxxh());
    }

    /**
     * 处理住院药品的追溯码库存信息
     * 参考阜阳人医的handleTracCodgStoreCydy方法逻辑
     *
     * @param extendedList 扩展的住院发药明细列表
     * @param token        令牌
     */
    public List<InPatientDispenseDetailBindScatteredVo> handleTracCodgStoreForInpatient(List<InPatientDispenseDetailBindScatteredVo> extendedList, String token) {
        try {
            // 1. 构建查询药品追溯信息的请求列表
            List<QueryTracDrugRequest> queryTracDrugRequestList = extendedList.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getCfmxxh()) && item.getSelRetnCnt() != null)
                    .map(item -> QueryTracDrugRequest.builder()
                            .cfxh(item.getCfxh())
                            .cfmxxh(item.getCfmxxh())
                            .drugCode(item.getFixmedinsHilistId())
                            .dispCnt(item.getSelRetnCnt())
                            .build())
                    .collect(Collectors.toList());

            if (queryTracDrugRequestList.isEmpty()) {
                log.info("没有需要查询追溯信息的药品");
                return extendedList;
            }

            // 调用SaaS接口批量查询药品追溯信息
            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(token, queryTracDrugRequestList);

            // 2. 处理追溯标志和转换比
            List<GetTracCodgStoreDataRequest> tracDataList = new ArrayList<>();
            for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
                if (queryTracDrugMap.containsKey(item.getCfmxxh())) {
                    QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(item.getCfmxxh());
                    // 设置追溯标志（住院药品使用trdnFlag字段）
                    item.setTrdnFlag(queryTracDrug.getIsTrac());
                    // 设置HIS转换比例
                    item.setHisConRatio(String.valueOf(queryTracDrug.getConRatio()));

                    // 如果药品需要追溯，收集需要查询库存的药品
                    if ("1".equals(queryTracDrug.getIsTrac())) {
                        GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder()
                                .cfxh(item.getCfxh())
                                .cfmxxh(item.getCfmxxh())
                                .dispCnt(item.getSelRetnCnt() != null ? item.getSelRetnCnt() : 0)
                                .drugCode(item.getFixmedinsHilistId())
                                .build();
                        tracDataList.add(build);
                    }
                }
            }

            // 3. 批量获取追溯码库存信息
            Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();
            if (!tracDataList.isEmpty()) {
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(token,
                        GetTracCodgStoreRequest.builder().dataList(tracDataList).build());
                tracCodgStoreMap = tracCodgStore.stream()
                        .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
            }

            // 4. 将库存信息填充回药品对象
            for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
                if ("1".equals(item.getTrdnFlag()) && tracCodgStoreMap.containsKey(item.getCfmxxh())) {
                    GetTracCodgStoreDataResponse tracCodgStoreData = tracCodgStoreMap.get(item.getCfmxxh());
                    item.setDrugCode(tracCodgStoreData.getDrugCode());
                    item.setDrugTracCodgs(tracCodgStoreData.getDrugTracCodgs());
                    item.setDispCnt(tracCodgStoreData.getDispCnt());
                    item.setCurrNum(tracCodgStoreData.getCurrNum());
                    item.setTracCodgStore(tracCodgStoreData);
                }
            }

            log.info("住院药品追溯码库存信息处理完成，共处理{}条数据，其中{}条需要追溯", extendedList.size(), tracDataList.size());

        } catch (Exception e) {
            log.error("处理住院药品追溯码库存信息异常", e);
            // 不抛出异常，避免影响主流程
        }
        return extendedList;
    }

    /**
     * 处理任务状态和数据过滤
     * 参考阜阳人医门诊处方查询中的任务状态处理逻辑
     * 过滤掉已完成扫码任务的药品信息，防止后续业务重复上传造成脏数据
     *
     * @param resultList 住院发药明细列表
     */
    private void filterByTaskStatus(List<InPatientDispenseDetailBindScatteredVo> resultList, InpatientPrescriptionQueryDto queryDt) {
        if (resultList == null || resultList.isEmpty()) {
            return;
        }

        // 处理每条处方数据，参考TongLingRenYiService的filterByTaskStatusForInpatient方法
        for (InPatientDispenseDetailBindScatteredVo item : resultList) {
            String outPresId = item.getRecordId() + "-" + item.getPatWardId();
            if (StringUtils.isEmpty(outPresId)) {
                continue;
            }
            
            // 默认未扫码
            item.setScanStatus("0");

            // 1. 查询是否存在已完成且未删除的任务
            LambdaQueryWrapper<YsfStoTcTask> completedTaskQuery = new LambdaQueryWrapper<>();
            completedTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                    .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode()) // 已完成
                    .eq(YsfStoTcTask::getDelFlag, "0"); // 未删除
            YsfStoTcTask tcTask = ysfStoTcTaskMapper.selectOne(completedTaskQuery);

            // 2. 如果存在已完成的任务，需要查询他的追溯码记录
            if (tcTask != null) {
                item.setScanStatus("1");
                appendTraceabilityInfoForCompletedTask(item, tcTask.getIdTask());
            }

            // 3. 如果不存在已完成的任务，则查询最新的待处理或已失效任务
            LambdaQueryWrapper<YsfStoTcTask> latestTaskQuery = new LambdaQueryWrapper<>();
            latestTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                    .in(YsfStoTcTask::getFgStatus, Arrays.asList(TaskStatusEnum.PENDING.getCode(), TaskStatusEnum.EXPIRED.getCode())) // 待处理或已失效
                    .eq(YsfStoTcTask::getDelFlag, "0") // 未删除
                    .orderByDesc(YsfStoTcTask::getIdTask)
                    .last("limit 1"); // 按创建时间降序

            YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(latestTaskQuery);

            // 4. 如果找到这样的任务，则在处方对象中追加任务相关字段
            if (latestTask != null) {
                item.setScanStatus("0");
                log.debug("住院处方[{}]找到待处理任务，任务ID：{}, 状态：{}", outPresId, latestTask.getIdTask(), latestTask.getFgStatus());

                // 查询任务明细信息
                LambdaQueryWrapper<YsfStoTcTaskSub> taskSubQuery = new LambdaQueryWrapper<>();
                taskSubQuery.eq(YsfStoTcTaskSub::getIdTask, latestTask.getIdTask())
                        .eq(YsfStoTcTaskSub::getDelFlag, "0")
                        .eq(YsfStoTcTaskSub::getCfmxxh, item.getRecordDetailId());
                YsfStoTcTaskSub taskSub = ysfStoTcTaskSubService.getOne(taskSubQuery);

                if (taskSub != null) {
                    // 设置追溯码相关信息
                    item.setTaskIdSubDps(taskSub.getIdSub() != null ?
                            String.valueOf(taskSub.getIdSub()) : null);
                    item.setTaskDrugtracinfoDps(taskSub.getDrugtracinfo());
                    item.setTaskFgScannedDps(taskSub.getFgScanned());
                    // 扫描时间格式化
                    if (taskSub.getScanTime() != null) {
                        item.setTaskDetailScanTimeDps(taskSub.getScanTime().toString());
                    }
                }
            }
        }
    }

    /**
     * 为已完成扫码任务的处方项追加追溯码信息
     * 参考TongLingRenYiService的appendTraceabilityInfoForCompletedTask方法
     * 
     * @param item 处方项
     * @param idTask 任务ID
     */
    private void appendTraceabilityInfoForCompletedTask(InPatientDispenseDetailBindScatteredVo item, Long idTask) {
        try {
            // 根据任务ID查询追溯码子表
            LambdaQueryWrapper<YsfStoTcTaskSub> taskSubQuery = new LambdaQueryWrapper<>();
            taskSubQuery.eq(YsfStoTcTaskSub::getIdTask, idTask)
                    .eq(YsfStoTcTaskSub::getDelFlag, "0")
                    .eq(YsfStoTcTaskSub::getCfmxxh, item.getRecordDetailId())
                    .isNotNull(YsfStoTcTaskSub::getDrugtracinfo)
                    .last("LIMIT 1");
            YsfStoTcTaskSub taskSub = ysfStoTcTaskSubService.getOne(taskSubQuery);
            
            if (taskSub != null) {
                // 设置扫描时间
                if (taskSub.getScanTime() != null) {
                    item.setLastScanTime(taskSub.getScanTime().toString());
                }
                // 设置追溯码信息
                item.setDrugTracInfo(taskSub.getDrugtracinfo());
                
                // 同时设置任务相关字段
                item.setTaskIdSubDps(taskSub.getIdSub() != null ? 
                        String.valueOf(taskSub.getIdSub()) : null);
                item.setTaskDrugtracinfoDps(taskSub.getDrugtracinfo());
                item.setTaskFgScannedDps(taskSub.getFgScanned());
                if (taskSub.getScanTime() != null) {
                    item.setTaskDetailScanTimeDps(taskSub.getScanTime().toString());
                }
            }
        } catch (Exception e) {
            log.error("为已完成任务追加追溯码信息异常，任务ID：{}，处方明细ID：{}", 
                    idTask, item.getRecordDetailId(), e);
        }
    }

    /**
     * 安全地解析整数
     * 支持解析带小数点的数值（如"133.0"），会自动转换为整数
     *
     * @param value 字符串值
     * @return 整数值，解析失败时返回0
     */
    private Integer parseIntegerSafely(String value) {
        try {
            if (StringUtils.isEmpty(value)) {
                return 0;
            }
            
            // 先尝试直接解析为整数
            try {
                return Integer.valueOf(value);
            } catch (NumberFormatException e) {
                // 如果直接解析失败，尝试先解析为浮点数再转换为整数
                Double doubleValue = Double.valueOf(value);
                return doubleValue.intValue();
            }
        } catch (NumberFormatException e) {
            log.warn("解析整数失败，值：{}，返回默认值0", value);
            return 0;
        }
    }

    /**
     * 验证查询参数
     *
     * @param queryDto 查询参数
     */
    private void validateQueryParams(OutpatientPrescriptionQueryDto queryDto) {
        if (StrUtil.isBlank(queryDto.getCardType())) {
            throw new BusinessException("查询类型(cardType)不能为空");
        }

        switch (queryDto.getCardType()) {
            case "1":
                if (StrUtil.isBlank(queryDto.getPatient_id())) {
                    throw new BusinessException("cardType=1时，患者ID(patient_id)不能为空");
                }
                break;
            case "2":
            case "3":
                if (StrUtil.isBlank(queryDto.getCardNo())) {
                    throw new BusinessException("cardType=2或3时，卡号(cardNo)不能为空");
                }
                break;
            case "4":
            case "5":
                if (StrUtil.isBlank(queryDto.getCfxh())) {
                    throw new BusinessException("cardType=4或5时，处方号(cfxh)不能为空");
                }
                break;
            case "6":
                if (StrUtil.isBlank(queryDto.getCfxh())) {
                    throw new BusinessException("cardType=6时，发票号(cfxh)不能为空");
                }
                break;
            default:
                throw new BusinessException("不支持的查询类型：" + queryDto.getCardType() + "，支持的类型：1-患者ID查询，2-身份证号查询，3-卡号查询，4-处方号查询，5-处方号查询患者所有处方");
        }
    }

    /**
     * 根据查询类型获取患者ID
     *
     * @param queryDto 查询参数
     * @return 患者ID，找不到时返回null或抛出异常
     */
    @Deprecated
    private String getPatientIdByQueryType(OutpatientPrescriptionQueryDto queryDto) {

        if (queryDto.getCardType().equals("1")) {
            return queryDto.getPatient_id();
        }

        // cardType=4时，使用cfxh查询，不需要获取患者ID
        if (queryDto.getCardType().equals("4")) {
            return null; // cfxh查询不需要患者ID
        }

        // cardType=5时，先通过cfxh查询处方，从结果中提取患者ID
        if (queryDto.getCardType().equals("5")) {
            return getPatientIdByCfxh(queryDto.getCfxh());
        }

        // 如果是cardType=2或3，通过卡号获取患者ID
        String patientId = getPatientId(queryDto.getCardType(), queryDto.getCardNo());
        if (StrUtil.isBlank(patientId)) {
            throw new BusinessException("未找到该卡号对应的患者信息");
        }
        return patientId;
    }


    /**
     * 通过卡号获取患者ID
     * 调用获取患者信息接口
     *
     * @param cardNo 卡号
     * @return 患者ID，找不到时返回null
     */
    private String getPatientId(String cardType, String cardNo) {
        try {
            log.info("开始通过卡号获取患者ID，卡号：{}", cardNo);

            // 构建查询患者信息的URL（使用GET请求，参数放在URL中）
            String requestUrl = hangChuangConfig.getPatientInfoUrl() +
                    "?queryType=" + cardType + "&queryValue=" + cardNo;

            // 发起GET请求
            String responseBody = HttpRequestUtil.executeGetRequest(requestUrl, "获取患者信息");

            // 解析响应结果
            return parsePatientInfoResponse(responseBody);

        } catch (Exception e) {
            log.error("通过卡号获取患者ID异常", e);
            return null;
        }
    }

    /**
     * 通过处方号获取患者ID
     * 先查询该处方的数据，从结果中提取患者ID
     *
     * @param cfxh 处方号
     * @return 患者ID，找不到时抛出异常
     */
    private String getPatientIdByCfxh(String cfxh) {
        try {
            log.info("开始通过处方号获取患者ID，处方号：{}", cfxh);

            // 构建处方查询参数，只使用cfxh
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("cfxh", cfxh);

            // 获取门诊发药明细接口URL
            String requestUrl = hangChuangConfig.getOutpatientDispenseDetailUrl();

            // 发起HTTP请求查询门诊发药明细
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "通过处方号查询患者ID");

            // 解析响应结果，提取患者ID
            List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList = parseOutpatientResponse(responseBody);

            if (resultList.isEmpty()) {
                throw new BusinessException("未找到处方号[" + cfxh + "]对应的处方信息");
            }

            // 从第一条数据中提取患者ID
            String patientId = resultList.get(0).getPatient_id();
            if (StrUtil.isBlank(patientId)) {
                throw new BusinessException("处方号[" + cfxh + "]对应的患者ID为空");
            }

            log.info("成功获取处方号[{}]对应的患者ID：{}", cfxh, patientId);
            return patientId;

        } catch (Exception e) {
            log.error("通过处方号获取患者ID异常，处方号：{}", cfxh, e);
            throw new BusinessException("通过处方号获取患者ID失败：" + e.getMessage());
        }
    }

    /**
     * 解析患者信息查询接口的响应
     *
     * @param responseBody 响应体
     * @return 患者ID，找不到时返回null
     */
    private String parsePatientInfoResponse(String responseBody) {
        try {
            // 解析响应结果
            JSONObject responseJson = JSONUtil.parseObj(responseBody);

            // 检查返回码
            String resultCode = responseJson.getStr("resultCode");
            if (!"1".equals(resultCode)) {
                log.warn("获取患者信息失败，返回码：{}，消息：{}", resultCode, responseJson.getStr("resultMessage"));
                return null;
            }

            // 获取患者数据
            Object data = responseJson.get("data");
            if (data == null) {
                log.warn("患者信息数据为空");
                return null;
            }

            // 解析患者列表
            List<Map<String, Object>> patientList = (List<Map<String, Object>>) data;
            if (patientList.isEmpty()) {
                log.warn("未找到对应的患者信息");
                return null;
            }

            // 获取第一个患者的ID
            Map<String, Object> patient = patientList.get(0);
            String patientId = (String) patient.get("patientId");

            log.info("成功获取患者ID：{}，患者姓名：{}", patientId, patient.get("clientName"));
            return patientId;

        } catch (Exception e) {
            log.error("解析患者信息响应异常", e);
            return null;
        }
    }

    /**
     * 构建门诊发药明细查询参数
     *
     * @param queryDto  查询DTO
     * @param patientId 患者ID
     * @return 请求参数Map
     */
    private Map<String, Object> buildOutpatientRequestParams(OutpatientPrescriptionQueryDto queryDto, String patientId) {
        Map<String, Object> params = new HashMap<>();

        // 根据cardType构建不同的查询参数
        switch (queryDto.getCardType()) {
            case "4":
                // cardType=4: 使用处方号(cfxh)查询
                if (StrUtil.isNotBlank(queryDto.getCfxh())) {
                    params.put("type", "1");
                    params.put("cfxh", queryDto.getCfxh());
                }
                break;
            case "6":
                // cardType=6: 使用发票号查询
                if (StrUtil.isNotBlank(queryDto.getCfxh())) {
                    params.put("type", "2");
                    params.put("cfxh", queryDto.getCfxh());
                }
                break;
            default:
                log.warn("不支持的查询类型: {}", queryDto.getCardType());
                break;
        }

        // 通用参数：时间范围
        if (StrUtil.isNotBlank(queryDto.getStartTime())) {
            params.put("start_time", queryDto.getStartTime());
        }

        if (StrUtil.isNotBlank(queryDto.getEndTime())) {
            params.put("end_time", queryDto.getEndTime());
        }

        // 其他可选参数
        if (StrUtil.isNotBlank(queryDto.getFg_dps())) {
            params.put("fg_dps", queryDto.getFg_dps());
        }

        if (StrUtil.isNotBlank(queryDto.getSend_flag())) {
            params.put("send_flag", queryDto.getSend_flag());
        }

        log.info("构建门诊发药明细查询参数完成，cardType={}，参数：{}", queryDto.getCardType(), JSONUtil.toJsonStr(params));
        return params;
    }

    /**
     * 解析门诊发药明细响应结果
     *
     * @param responseBody 响应体
     * @return 门诊处方明细列表
     */
    private List<OutpatientPrescriptionResponseVo.PrescriptionItem> parseOutpatientResponse(String responseBody) {
        try {
            // 解析JSON响应
            JSONObject responseJson = JSONUtil.parseObj(responseBody);

            // 检查响应是否成功
            if (responseJson.containsKey("code")) {
                Integer code = responseJson.getInt("code");
                if (code != null && code != 0) {
                    String message = responseJson.getStr("message", "接口返回异常");
                    throw new BusinessException("接口返回错误：" + message);
                }
            }

            // 直接将dataList转换为OutpatientPrescriptionVo列表
            List<OutpatientPrescriptionVo> dataList = null;
            if (responseJson.containsKey("dataList")) {
                dataList = JSONUtil.toList(responseJson.getJSONArray("dataList"), OutpatientPrescriptionVo.class);
            }

            if (dataList == null || dataList.isEmpty()) {
                log.warn("门诊发药明细数据为空");
                return new ArrayList<>();
            }

            // 检查必要字段,只保存西药
            String[] arr = {"X", "Z"};
            // 转换为PrescriptionItem对象列表
            List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList = new ArrayList<>();
            for (OutpatientPrescriptionVo voItem : dataList) {
                // 仅保留以指定前缀开头的med_list_codg，否则跳过
                String medListCodg = voItem.getMed_list_codg();
                boolean startsWithAllowedPrefix = false;
                if (StringUtils.isNotEmpty(medListCodg)) {
                    for (String prefix : arr) {
                        if (medListCodg.startsWith(prefix)) {
                            startsWithAllowedPrefix = true;
                            break;
                        }
                    }
                }
                if (!startsWithAllowedPrefix) {
                    continue;
                }

                OutpatientPrescriptionResponseVo.PrescriptionItem prescriptionItem =
                        convertVoToPrescriptionItem(voItem);
                if (prescriptionItem != null) {
                    resultList.add(prescriptionItem);
                }
            }

            // 合并不同批次号bchno的药品,相同药品id的药品

            return mergeDrugsByMedListCodg(resultList);

        } catch (Exception e) {
            log.error("解析门诊发药明细响应异常", e);
            throw new BusinessException("解析门诊发药明细响应失败：" + e.getMessage(), e);
        }
    }

    /**
     * 将OutpatientPrescriptionVo数据转换为PrescriptionItem对象
     *
     * @param voItem OutpatientPrescriptionVo对象
     * @return PrescriptionItem对象
     */
    private OutpatientPrescriptionResponseVo.PrescriptionItem convertVoToPrescriptionItem(OutpatientPrescriptionVo voItem) {
        try {
            OutpatientPrescriptionResponseVo.PrescriptionItem item =
                    new OutpatientPrescriptionResponseVo.PrescriptionItem();

            // 直接使用实体类字段进行赋值，避免Map取值方式
            item.setMed_list_codg(voItem.getMed_list_codg());
            item.setFixmedins_hilist_id(voItem.getFixmedins_hilist_id());
            item.setFixmedins_hilist_name(voItem.getFixmedins_hilist_name());
            item.setFixmedins_bchno(voItem.getFixmedins_bchno());
            item.setPrsc_dr_name(voItem.getPrsc_dr_name());
            item.setPhar_name(voItem.getPhar_name());
            item.setPhar_prac_cert_no(voItem.getPhar_prac_cert_no());
            item.setMdtrt_sn(voItem.getMdtrt_sn());
            item.setPsn_name(voItem.getPsn_name());
            item.setManu_lotnum(voItem.getManu_lotnum());
            item.setExpy_end(voItem.getExpy_end());

            // 处理数据类型转换：OutpatientPrescriptionVo中是String，PrescriptionItem中是Integer
            item.setRx_flag(parseStringToInteger(voItem.getRx_flag()));
            item.setTrdn_flag(parseStringToInteger(voItem.getTrdn_flag()));

            item.setRxno(voItem.getRxno());
            item.setRx_circ_flag(voItem.getRx_circ_flag());
            item.setRtal_docno(voItem.getRtal_docno());
            item.setStoout_no(voItem.getStoout_no());
            item.setBchno(voItem.getBchno());

            // 处理数据类型转换：OutpatientPrescriptionVo中是Integer，PrescriptionItem中是String
            item.setSel_retn_cnt(parseIntegerToString(voItem.getSel_retn_cnt()));
            item.setMin_sel_retn_cnt(parseIntegerToString(voItem.getMin_sel_retn_cnt()));

            item.setSelRetnUnit(voItem.getSel_retn_unit());
            item.setHisDosUnit(voItem.getHis_dos_unit());
            item.setHisPacUnit(voItem.getHis_pac_unit());
            item.setSel_retn_time(voItem.getSel_retn_time());
            item.setSel_retn_opter_name(voItem.getSel_retn_opter_name());

            // 处理数据类型转换：OutpatientPrescriptionVo中是String，PrescriptionItem中是Integer
            item.setMdtrt_setl_type(parseStringToInteger(voItem.getMdtrt_setl_type()));

            item.setSpec(voItem.getSpec());
            item.setProdentp_name(voItem.getProdentp_name());
            item.setCfxh(voItem.getCfxh());
            item.setCfmxxh(voItem.getCfmxxh());
            item.setSjh(voItem.getSjh());
            item.setPatient_id(voItem.getPatient_id());
            item.setHis_con_ratio(voItem.getHis_con_ratio());

            // 处理数据类型转换：OutpatientPrescriptionVo中是String，PrescriptionItem中是Integer
            item.setSend_flag(parseStringToInteger(voItem.getSend_flag()));
            item.setSend_time(voItem.getSend_time());

            // 复制SaaS拆零接口字段
            item.setDrugCode(voItem.getDrugCode());
            item.setDrugTracCodgs(voItem.getDrugTracCodgs());
            item.setDispCnt(voItem.getDispCnt());
            item.setCurrNum(voItem.getCurrNum());

            // 新增字段映射
            item.setDept_id(voItem.getDept_id());
            item.setDept_name(voItem.getDept_name());
            item.setWindow(voItem.getWindow());

            return item;

        } catch (Exception e) {
            log.error("转换OutpatientPrescriptionVo到PrescriptionItem异常", e);
            return null;
        }
    }

    /**
     * 安全地将字符串转换为整数
     *
     * @param value 字符串值
     * @return 整数值，解析失败时返回null
     */
    private Integer parseStringToInteger(String value) {
        try {
            return StringUtils.isNotEmpty(value) ? Integer.valueOf(value) : null;
        } catch (NumberFormatException e) {
            log.warn("解析字符串到整数失败，值：{}，返回null", value);
            return null;
        }
    }

    /**
     * 安全地将整数转换为字符串
     *
     * @param value 整数值
     * @return 字符串值，null时返回null
     */
    private String parseIntegerToString(Integer value) {
        return value != null ? value.toString() : null;
    }

    /**
     * 按照med_list_codg分组合并药品记录
     * 解决同一药品因不同批次而被拆分成多个明细记录的问题
     *
     * @param originalList 原始药品记录列表
     * @return 合并后的药品记录列表
     */
    private List<OutpatientPrescriptionResponseVo.PrescriptionItem> mergeDrugsByMedListCodg(
            List<OutpatientPrescriptionResponseVo.PrescriptionItem> originalList) {
        
        if (originalList == null || originalList.isEmpty()) {
            return originalList;
        }

        log.info("开始合并药品数据，原始数据量：{}", originalList.size());

        // 按med_list_codg分组
        Map<String, List<OutpatientPrescriptionResponseVo.PrescriptionItem>> groupedByMedListCodg = 
            originalList.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getMed_list_codg()))
                .collect(Collectors.groupingBy(OutpatientPrescriptionResponseVo.PrescriptionItem::getMed_list_codg));

        List<OutpatientPrescriptionResponseVo.PrescriptionItem> mergedList = new ArrayList<>();
        int mergedCount = 0;

        for (Map.Entry<String, List<OutpatientPrescriptionResponseVo.PrescriptionItem>> entry : groupedByMedListCodg.entrySet()) {
            String medListCodg = entry.getKey();
            List<OutpatientPrescriptionResponseVo.PrescriptionItem> sameCodeItems = entry.getValue();

            if (sameCodeItems.size() == 1) {
                // 只有一条记录，无需合并
                mergedList.add(sameCodeItems.get(0));
            } else {
                // 多条记录，需要合并
                OutpatientPrescriptionResponseVo.PrescriptionItem mergedItem = mergeSameMedListCodgItems(sameCodeItems);
                if (mergedItem != null) {
                    mergedList.add(mergedItem);
                    mergedCount++;
                    log.debug("药品 {} 合并了 {} 条记录", medListCodg, sameCodeItems.size());
                }
            }
        }

        log.info("药品数据合并完成，原始数据：{} 条，合并后：{} 条，实际合并了 {} 个药品组",
                originalList.size(), mergedList.size(), mergedCount);

        return mergedList;
    }

    /**
     * 合并相同med_list_codg的药品记录
     *
     * @param sameCodeItems 相同药品编码的记录列表
     * @return 合并后的记录
     */
    private OutpatientPrescriptionResponseVo.PrescriptionItem mergeSameMedListCodgItems(
            List<OutpatientPrescriptionResponseVo.PrescriptionItem> sameCodeItems) {
        
        if (sameCodeItems == null || sameCodeItems.isEmpty()) {
            return null;
        }

        // 使用第一条记录作为基础模板
        OutpatientPrescriptionResponseVo.PrescriptionItem mergedItem = 
            new OutpatientPrescriptionResponseVo.PrescriptionItem();
        OutpatientPrescriptionResponseVo.PrescriptionItem firstItem = sameCodeItems.get(0);

        // 复制基础字段（这些字段在同一药品的不同批次中应该是相同的）
        copyBasicFieldsForMerge(firstItem, mergedItem, sameCodeItems);

        // 计算合并字段
        calculateMergedFields(sameCodeItems, mergedItem);

        return mergedItem;
    }

    /**
     * 复制基础字段用于合并
     *
     * @param source 源记录
     * @param target 目标记录
     * @param sameCodeItems 相同药品编码的记录列表，用于合并批次字段
     */
    private void copyBasicFieldsForMerge(OutpatientPrescriptionResponseVo.PrescriptionItem source,
                                        OutpatientPrescriptionResponseVo.PrescriptionItem target,
                                        List<OutpatientPrescriptionResponseVo.PrescriptionItem> sameCodeItems) {
        target.setMed_list_codg(source.getMed_list_codg());
        target.setFixmedins_hilist_id(source.getFixmedins_hilist_id());
        target.setFixmedins_hilist_name(source.getFixmedins_hilist_name());
        target.setFixmedins_bchno(source.getFixmedins_bchno());
        target.setPrsc_dr_name(source.getPrsc_dr_name());
        target.setPhar_name(source.getPhar_name());
        target.setPhar_prac_cert_no(source.getPhar_prac_cert_no());
        target.setMdtrt_sn(source.getMdtrt_sn());
        target.setPsn_name(source.getPsn_name());
        target.setRx_flag(source.getRx_flag());
        target.setTrdn_flag(source.getTrdn_flag());
        target.setRxno(source.getRxno());
        target.setRx_circ_flag(source.getRx_circ_flag());
        target.setRtal_docno(source.getRtal_docno());
        target.setStoout_no(source.getStoout_no());
        target.setSelRetnUnit(source.getSelRetnUnit());
        target.setHisDosUnit(source.getHisDosUnit());
        target.setHisPacUnit(source.getHisPacUnit());
        target.setSel_retn_time(source.getSel_retn_time());
        target.setSel_retn_opter_name(source.getSel_retn_opter_name());
        target.setMdtrt_setl_type(source.getMdtrt_setl_type());
        target.setSpec(source.getSpec());
        target.setProdentp_name(source.getProdentp_name());
        target.setCfxh(source.getCfxh());
        target.setCfmxxh(source.getCfmxxh());
        target.setSjh(source.getSjh());
        target.setPatient_id(source.getPatient_id());
        target.setHis_con_ratio(source.getHis_con_ratio());
        target.setSend_flag(source.getSend_flag());
        target.setSend_time(source.getSend_time());
        target.setDept_id(source.getDept_id());
        target.setDept_name(source.getDept_name());
        target.setWindow(source.getWindow());

        // 对于批次相关字段，只取第一个值
        target.setManu_lotnum(source.getManu_lotnum());
        target.setExpy_end(source.getExpy_end());
        target.setBchno(source.getBchno());
    }

    /**
     * 计算合并后的数值字段
     *
     * @param sameCodeItems 相同药品编码的记录列表
     * @param mergedItem 合并后的记录
     */
    private void calculateMergedFields(List<OutpatientPrescriptionResponseVo.PrescriptionItem> sameCodeItems,
                                      OutpatientPrescriptionResponseVo.PrescriptionItem mergedItem) {
        
        // 1. 计算min_sel_retn_cnt的总和
        int totalMinSelRetnCnt = sameCodeItems.stream()
                .mapToInt(item -> parseIntegerSafely(item.getMin_sel_retn_cnt()))
                .sum();
        mergedItem.setMin_sel_retn_cnt(String.valueOf(totalMinSelRetnCnt));

        // 2. 计算sel_retn_cnt的总和
        int totalSelRetnCnt = sameCodeItems.stream()
                .mapToInt(item -> parseIntegerSafely(item.getSel_retn_cnt()))
                .sum();

        // 3. 重新计算sel_retn_cnt值
        String finalSelRetnCnt = calculateFinalSelRetnCnt(totalSelRetnCnt, mergedItem.getHis_con_ratio());
        mergedItem.setSel_retn_cnt(finalSelRetnCnt);

        log.debug("药品 {} 合并计算：min_sel_retn_cnt总和={}, sel_retn_cnt总和={}, 最终sel_retn_cnt={}, his_con_ratio={}",
                mergedItem.getMed_list_codg(), totalMinSelRetnCnt, totalSelRetnCnt, finalSelRetnCnt, mergedItem.getHis_con_ratio());
    }

    /**
     * 计算最终的sel_retn_cnt值
     * 根据业务规则：将总和除以his_con_ratio，如果结果为正整数则使用，否则使用总和
     *
     * @param totalSelRetnCnt sel_retn_cnt的总和
     * @param hisConRatio his_con_ratio值
     * @return 最终的sel_retn_cnt值
     */
    private String calculateFinalSelRetnCnt(int totalSelRetnCnt, String hisConRatio) {
        try {
            if (StringUtils.isEmpty(hisConRatio) || totalSelRetnCnt <= 0) {
                return String.valueOf(totalSelRetnCnt);
            }

            // 解析his_con_ratio
            BigDecimal conRatio = new BigDecimal(hisConRatio);
            if (conRatio.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("his_con_ratio值无效：{}，使用总和作为sel_retn_cnt", hisConRatio);
                return String.valueOf(totalSelRetnCnt);
            }

            // 计算除法结果
            BigDecimal totalBigDecimal = new BigDecimal(totalSelRetnCnt);
            BigDecimal divisionResult = totalBigDecimal.divide(conRatio, 10, java.math.RoundingMode.HALF_UP);

            // 检查是否为正整数
            if (divisionResult.scale() == 0 || divisionResult.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
                int intResult = divisionResult.intValue();
                if (intResult > 0) {
                    log.debug("除法结果为正整数：{} ÷ {} = {}", totalSelRetnCnt, hisConRatio, intResult);
                    return String.valueOf(intResult);
                }
            }

            // 不是正整数，使用总和
            log.debug("除法结果不是正整数：{} ÷ {} = {}，使用总和 {}", 
                    totalSelRetnCnt, hisConRatio, divisionResult, totalSelRetnCnt);
            return String.valueOf(totalSelRetnCnt);

        } catch (Exception e) {
            log.warn("计算sel_retn_cnt时发生异常，使用总和作为结果：totalSelRetnCnt={}, hisConRatio={}", 
                    totalSelRetnCnt, hisConRatio, e);
            return String.valueOf(totalSelRetnCnt);
        }
    }


    /**
     * 处理门诊药品的追溯码库存信息
     * 参考阜阳人医的handleTracCodgStoreCydy方法逻辑
     *
     * @param resultList 门诊处方明细列表
     * @param token      令牌
     */
    private void handleTracCodgStoreForOutpatient(List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList, String token) {
        try {
            // 1. 构建查询药品追溯信息的请求列表
            List<QueryTracDrugRequest> queryTracDrugRequestList = resultList.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getCfmxxh()) && StringUtils.isNotEmpty(item.getSel_retn_cnt()))
                    .map(item -> QueryTracDrugRequest.builder()
                            .cfxh(item.getCfxh())
                            .cfmxxh(item.getCfmxxh())
                            .drugCode(item.getFixmedins_hilist_id())
                            .dispCnt(parseIntegerSafely(item.getMin_sel_retn_cnt()))
                            .build())
                    .collect(Collectors.toList());

            if (queryTracDrugRequestList.isEmpty()) {
                log.info("没有需要查询追溯信息的门诊药品");
                return;
            }

            // 调用SaaS接口批量查询药品追溯信息
            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(token, queryTracDrugRequestList);

            // 2. 处理追溯标志和转换比
            List<GetTracCodgStoreDataRequest> tracDataList = new ArrayList<>();
            for (OutpatientPrescriptionResponseVo.PrescriptionItem item : resultList) {
                if (queryTracDrugMap.containsKey(item.getCfmxxh())) {
                    QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(item.getCfmxxh());
                    // 设置追溯标志
                    item.setTrdn_flag(Integer.valueOf(queryTracDrug.getIsTrac()));
                    // 设置HIS转换比例
                    item.setHis_con_ratio(String.valueOf(queryTracDrug.getConRatio()));

                    // 如果药品需要追溯，收集需要查询库存的药品
                    if ("1".equals(queryTracDrug.getIsTrac())) {
                        GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder()
                                .cfxh(item.getCfxh())
                                .cfmxxh(item.getCfmxxh())
                                .dispCnt(parseIntegerSafely(item.getMin_sel_retn_cnt()))
                                .drugCode(item.getFixmedins_hilist_id())
                                .build();
                        tracDataList.add(build);
                    }
                }
            }

            // 3. 批量获取追溯码库存信息
            Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();
            if (!tracDataList.isEmpty()) {
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(token,
                        GetTracCodgStoreRequest.builder().dataList(tracDataList).build());
                tracCodgStoreMap = tracCodgStore.stream()
                        .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
            }

            // 4. 将库存信息填充回药品对象
            for (OutpatientPrescriptionResponseVo.PrescriptionItem item : resultList) {
                if (item.getTrdn_flag() != null && item.getTrdn_flag() == 1 && tracCodgStoreMap.containsKey(item.getCfmxxh())) {
                    GetTracCodgStoreDataResponse tracCodgStoreData = tracCodgStoreMap.get(item.getCfmxxh());
                    item.setDrugCode(tracCodgStoreData.getDrugCode());
                    item.setDrugTracCodgs(tracCodgStoreData.getDrugTracCodgs());
                    item.setDispCnt(tracCodgStoreData.getDispCnt());
                    item.setCurrNum(tracCodgStoreData.getCurrNum());
                    item.setTracCodgStore(tracCodgStoreData);
                }
            }

            log.info("门诊药品追溯码库存信息处理完成，共处理{}条数据，其中{}条需要追溯", resultList.size(), tracDataList.size());

        } catch (Exception e) {
            log.error("处理门诊药品追溯码库存信息异常", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 处理门诊任务状态和数据过滤
     * 参考阜阳人医门诊处方查询中的任务状态处理逻辑
     * 过滤掉已完成扫码任务的药品信息，防止后续业务重复上传造成脏数据
     *
     * @param resultList 门诊处方明细列表
     */
    private void filterByTaskStatusForOutpatient(List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList) {
        if (resultList == null || resultList.isEmpty()) {
            return;
        }

        // 处理每条处方数据,过滤掉已完成扫码任务的药品信息,防止后续业务重复上传造成脏数据
        Iterator<OutpatientPrescriptionResponseVo.PrescriptionItem> iterator = resultList.iterator();
        while (iterator.hasNext()) {
            OutpatientPrescriptionResponseVo.PrescriptionItem dispenseInfo = iterator.next();
            String outPresId = dispenseInfo.getCfxh();
            if (StringUtils.isEmpty(outPresId)) {
                continue;
            }

            // 1. 查询是否存在已完成且未删除的任务
            LambdaQueryWrapper<YsfStoTcTask> completedTaskQuery = new LambdaQueryWrapper<>();
            completedTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                    .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode()) // 已完成
                    .eq(YsfStoTcTask::getDelFlag, "0"); // 未删除
            Long completedTaskCount = ysfStoTcTaskMapper.selectCount(completedTaskQuery);

            // 2. 如果存在已完成的任务，则过滤掉该处方数据
            if (completedTaskCount > 0) {
                iterator.remove();
                continue;
            }

            // 3. 如果不存在已完成的任务，则查询最新的待处理或已失效任务
            LambdaQueryWrapper<YsfStoTcTask> latestTaskQuery = new LambdaQueryWrapper<>();
            latestTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                    .in(YsfStoTcTask::getFgStatus, Arrays.asList(TaskStatusEnum.PENDING.getCode(), TaskStatusEnum.EXPIRED.getCode())) // 待处理或已失效
                    .eq(YsfStoTcTask::getDelFlag, "0") // 未删除
                    .orderByDesc(YsfStoTcTask::getIdTask)
                    .last("limit 1"); // 按创建时间降序

            YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(latestTaskQuery);

            // 4. 如果找到这样的任务，则在处方对象中追加任务相关字段
            if (latestTask != null) {
                // 使用实体类中新增的字段
                dispenseInfo.setTaskIdDps(latestTask.getIdTask() != null ?
                        latestTask.getIdTask().toString() : null);
                dispenseInfo.setTaskFgStatusDps(latestTask.getFgStatus());
                dispenseInfo.setTaskScanTimeDps(latestTask.getCreateTime() != null ?
                        latestTask.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
            }
        }
    }

    /**
     * 构建住院请求参数
     *
     * @param queryDto 查询DTO
     * @return 请求参数Map
     */
    private Map<String, Object> buildRequestParams(InpatientPrescriptionQueryDto queryDto) {
        Map<String, Object> params = new HashMap<>();

        // 根据需求描述中的curl示例构建参数
        if (StrUtil.isNotBlank(queryDto.getStartTime())) {
            params.put("start_time", queryDto.getStartTime());
        }

        if (StrUtil.isNotBlank(queryDto.getEndTime())) {
            params.put("end_time", queryDto.getEndTime());
        }

        // record_id 和 pat_in_hos_id 必须填一个
        if (StrUtil.isNotBlank(queryDto.getRecordId())) {
            params.put("record_id", queryDto.getRecordId());
        } else if (StrUtil.isNotBlank(queryDto.getPatInHosId())) {
            params.put("pat_in_hos_id", queryDto.getPatInHosId());
        } else {
            throw new BusinessException("record_id 和 pat_in_hos_id 必须填写其中一个参数");
        }

        // fg_dps 参数必填
        params.put("fg_dps", StrUtil.isNotBlank(queryDto.getFgDps()) ? queryDto.getFgDps() : "0");

        // 其他可选参数
        if (StrUtil.isNotBlank(queryDto.getFyyf())) {
            params.put("fyyf", queryDto.getFyyf());
        }

        if (StrUtil.isNotBlank(queryDto.getDeptId())) {
            params.put("dept_id", queryDto.getDeptId());
        }

        if (StrUtil.isNotBlank(queryDto.getPatWardId())) {
            params.put("pat_ward_id", queryDto.getPatWardId());
        }

        return params;
    }

    /**
     * 同步药品字典
     *
     * @return 同步结果
     */
    public ApiResult<String> syncDrugDictionary() {
        log.info("开始同步HIS药品字典到DMH平台");

        try {
            // 获取SaaS用户信息
            AccessTokenReponse userInfo = SaasHttpUtil.getAccessToken(DrugDictAccountConstant.user, DrugDictAccountConstant.password);

            // 1. 分页拉取HIS系统全量药品数据
            List<HisDrugCatalogResponse.HisDrugCatalogItem> allDrugList = fetchAllDrugsFromHIS();
            if (allDrugList.isEmpty()) {
                log.warn("从HIS系统未获取到任何药品数据");
                return ApiResult.success("未获取到药品数据，同步完成");
            }

            log.info("从HIS系统共获取到{}条药品数据", allDrugList.size());

            // 2. 数据转换：将HIS数据转换为DMH平台要求的格式
            List<HisDrugInfoSaasApiData> convertedDrugList = convertHisDrugsToDmhFormat(allDrugList);
            log.info("成功转换{}条药品数据", convertedDrugList.size());

            // 3. 分批上传到DMH平台
            int batchSize = 200; // 每批200条
            int totalBatches = (int) Math.ceil((double) convertedDrugList.size() / batchSize);
            int uploadedCount = 0;

            for (int i = 0; i < totalBatches; i++) {
                int fromIndex = i * batchSize;
                int toIndex = Math.min((i + 1) * batchSize, convertedDrugList.size());
                List<HisDrugInfoSaasApiData> batchData = convertedDrugList.subList(fromIndex, toIndex);

                // 上传单批数据
                boolean batchSuccess = uploadDrugBatchToDmh(batchData, i + 1, totalBatches, userInfo);
                if (batchSuccess) {
                    uploadedCount += batchData.size();
                    log.info("第{}/{}批药品数据上传成功，当前批次{}条，累计上传{}条",
                            i + 1, totalBatches, batchData.size(), uploadedCount);
                } else {
                    log.error("第{}/{}批药品数据上传失败", i + 1, totalBatches);
                    return ApiResult.error(String.format("第%d批药品数据上传失败，已成功上传%d条", i + 1, uploadedCount));
                }

                // 避免请求过于频繁，添加间隔
                if (i < totalBatches - 1) {
                    try {
                        Thread.sleep(1000); // 1秒间隔
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("同步过程中断");
                        break;
                    }
                }
            }

            String successMessage = String.format("药品字典同步完成！共处理%d条数据，成功上传%d条",
                    convertedDrugList.size(), uploadedCount);
            log.info(successMessage);
            return ApiResult.success(successMessage);

        } catch (Exception e) {
            log.error("同步药品字典异常", e);
            return ApiResult.error("同步药品字典失败：" + e.getMessage());
        }
    }

    /**
     * 分页拉取HIS系统全量药品数据
     *
     * @return 全量药品数据列表
     */
    private List<HisDrugCatalogResponse.HisDrugCatalogItem> fetchAllDrugsFromHIS() {
        List<HisDrugCatalogResponse.HisDrugCatalogItem> allDrugList = new ArrayList<>();
        int currentPage = 1;
        int totalPages = 1;

        do {
            try {
                log.info("开始拉取HIS药品数据，第{}/{}页", currentPage, totalPages);

                // 构建请求参数
                Map<String, Object> requestParams = new HashMap<>();
                requestParams.put("pageNo", currentPage);
                requestParams.put("pageSize", 1000);


                // 发起HTTP请求
                String responseBody = HttpUtil.get(hangChuangConfig.getHisDrugCatalogUrl(), requestParams);

                // 解析响应结果
                HisDrugCatalogResponse response = HttpRequestUtil.parseResponseToObject(responseBody, HisDrugCatalogResponse.class);

                if (response == null) {
                    log.error("解析HIS药品目录响应失败，第{}页", currentPage);
                    break;
                }

                // 更新总页数
                if (response.getTotalPageCount() != null) {
                    totalPages = response.getTotalPageCount();
                }

                // 添加当前页的数据
                if (response.getResultList() != null && !response.getResultList().isEmpty()) {
                    allDrugList.addAll(response.getResultList());
                    log.info("第{}/{}页获取到{}条药品数据，累计{}条",
                            currentPage, totalPages, response.getResultList().size(), allDrugList.size());
                } else {
                    log.warn("第{}页未获取到药品数据", currentPage);
                }

                currentPage++;

            } catch (Exception e) {
                log.error("拉取HIS药品数据异常，第{}页", currentPage, e);
                break;
            }

        } while (currentPage <= totalPages);

        log.info("HIS药品数据拉取完成，共获取{}条数据", allDrugList.size());
        return allDrugList;
    }

    /**
     * 将HIS药品数据转换为DMH平台要求的格式
     *
     * @param hisDrugList HIS药品数据列表
     * @return 转换后的药品数据列表
     */
    private List<HisDrugInfoSaasApiData> convertHisDrugsToDmhFormat(List<HisDrugCatalogResponse.HisDrugCatalogItem> hisDrugList) {
        log.info("开始转换药品数据格式，共{}条待转换", hisDrugList.size());

        List<HisDrugInfoSaasApiData> convertedList = new ArrayList<>();
        int convertedCount = 0;
        int skippedCount = 0;

        for (HisDrugCatalogResponse.HisDrugCatalogItem hisItem : hisDrugList) {
            try {
                HisDrugInfoSaasApiData dmhItem = new HisDrugInfoSaasApiData();

                // 按照文档映射规则进行转换

                // 1. hisDrugId = hisDrugCode + "-" + hisDrugManufacturerCode（核心规则）
                if (StringUtils.isNotEmpty(hisItem.getHisDrugCode()) &&
                        StringUtils.isNotEmpty(hisItem.getHisDrugManufacturerCode())) {
                    dmhItem.setHisDrugId(hisItem.getHisDrugCode() + "-" + hisItem.getHisDrugManufacturerCode());
                } else {
                    log.warn("药品代码或生产企业编码为空，跳过该药品：hisDrugCode={}, hisDrugManufacturerCode={}",
                            hisItem.getHisDrugCode(), hisItem.getHisDrugManufacturerCode());
                    skippedCount++;
                    continue;
                }

                // 2. hisDrugName：直接映射
                dmhItem.setHisDrugName(hisItem.getHisDrugName());

                // 3. hisDrugCountryCode：映射自drugUnino
                dmhItem.setHisDrugCountryCode(hisItem.getDrugUnino());

                // 4. hisDrugCountryName：直接映射
                dmhItem.setHisDrugCountryName(hisItem.getHisDrugCountryName());

                // 5. hisDrugSpec：直接映射
                dmhItem.setHisDrugSpec(hisItem.getHisDrugSpec());

                // 6. hisPackUnit：映射自hisDrugUnit（包装单位）
                dmhItem.setHisPackUnit(hisItem.getHisDrugUnit());

                // 7. hisDrugManufacturerCode：直接映射
                dmhItem.setHisDrugManufacturerCode(hisItem.getHisDrugManufacturerCode());

                // 8. hisDrugManufacturerName：直接映射
                dmhItem.setHisDrugManufacturerName(hisItem.getHisDrugManufacturerName());

                // 9. hisPurchaseUnit：建议映射（采购单位通常就是包装单位）
                dmhItem.setHisPurchaseUnit(hisItem.getHisDrugUnit());

                // 10. hisDoseForm：映射自dosform
                dmhItem.setHisDoseForm(hisItem.getDosform());

                // 11. hisApprovalNum：映射自aprvno（可能为空）
                dmhItem.setHisApprovalNum(hisItem.getAprvno());

                // 12. hisPacUnit：映射自hisPacUnit（最小包装单位）
                dmhItem.setHisPacUnit(hisItem.getHisPacUnit());

                // 13. hisPac：映射自hisPac（字符型）
                dmhItem.setHisPac(hisItem.getHisPac());

                // 14. hisConRatio：映射自hisPac（数值型，转换比即为包装数量）
                if (StringUtils.isNotEmpty(hisItem.getHisPac())) {
                    try {
                        dmhItem.setHisConRatio(new BigDecimal(hisItem.getHisPac()));
                    } catch (NumberFormatException e) {
                        log.warn("转换比解析失败，使用默认值1：hisPac={}", hisItem.getHisPac());
                        dmhItem.setHisConRatio(BigDecimal.ONE);
                    }
                } else {
                    dmhItem.setHisConRatio(BigDecimal.ONE);
                }

                // 15. delFlag：设置固定值0（数据有效）
                dmhItem.setDelFlag(0);

                // 其他字段设置为null或留空
                dmhItem.setHisEnterpriseCode(null);
                dmhItem.setHisEnterpriseName(null);
                dmhItem.setHisPurchasePrice(hisItem.getHisPurchasePrice());
                dmhItem.setHisDosUnit(null);
                dmhItem.setWholeQuantity(null);
                dmhItem.setHisDiscRate(null);
                dmhItem.setMemo(null);

                convertedList.add(dmhItem);
                convertedCount++;

            } catch (Exception e) {
                log.error("转换药品数据异常，跳过该药品：{}", JSONUtil.toJsonStr(hisItem), e);
                skippedCount++;
            }
        }

        log.info("药品数据转换完成，成功转换{}条，跳过{}条", convertedCount, skippedCount);
        return convertedList;
    }

    /**
     * 上传单批药品数据到DMH平台
     *
     * @param batchData    批次数据
     * @param batchIndex   批次索引
     * @param totalBatches 总批次数
     * @param userInfo     用户信息
     * @return 上传是否成功
     */
    private boolean uploadDrugBatchToDmh(List<HisDrugInfoSaasApiData> batchData, int batchIndex,
                                         int totalBatches, AccessTokenReponse userInfo) {
        try {
            // 构建请求对象
            HisDrugInfoSaasRequest request = new HisDrugInfoSaasRequest();
            request.setMedicalCode(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            request.setMedicalName(NhsaAccountConstant.getNhsaAccount().getMedicalName());
            request.setDataList(batchData);

            // 生成唯一的requestID
            String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            String requestID = String.format("DRUG-SYNC-%s-B%03d", timestamp, batchIndex);
            request.setRequestID(requestID);

            log.info("开始上传第{}/{}批药品数据到DMH平台，批次大小：{}，requestID：{}",
                    batchIndex, totalBatches, batchData.size(), requestID);

            // 调用SaaS接口上传
            SaasCommmonListResponse<UploadHisDrugInfoResponse> response =
                    SaasHttpUtil.uploadHisDrugInfo(userInfo.getAuthorization(), request);

            if (Objects.equals(response.getReturnCode(), 0)) {
                log.info("第{}/{}批药品数据上传成功，requestID：{}，响应：{}",
                        batchIndex, totalBatches, requestID, JSONUtil.toJsonStr(response));
                return true;
            } else {
                log.error("第{}/{}批药品数据上传失败，requestID：{}，响应：{}", batchIndex, totalBatches, requestID, JSONUtil.toJsonStr(response));
                return false;
            }

        } catch (Exception e) {
            log.error("第{}/{}批药品数据上传异常", batchIndex, totalBatches, e);
            return false;
        }
    }

    /**
     * 处理住院患者药品追溯码补录和上传流程
     * 按照上周日到本周六的时间范围，提升住院采集率
     *
     * @return 处理结果
     */
    public ApiResult<String> processWeeklyInpatientTraceability(String startDate, String endDate) {
        // 计算上周日到本周六的日期范围
        DateRange weeklyRange = DateRangeUtil.calculateCurrentWeekRange();
        log.info("开始处理住院患者药品追溯码补录，当前统计周期的,请检查`上周日`到`本周六`的时间范围：{} 至 {}",
                weeklyRange.getStartDate(), weeklyRange.getEndDate());

        try {
            // Step 1: 查询住院患者列表（使用周范围）
            List<InpatientSettlementVo> inpatients = queryInpatientsList(startDate, endDate);

            if (inpatients.isEmpty()) {
                log.info("指定时间范围内无住院患者");
                return ApiResult.success("指定时间范围内无住院患者");
            }

            // Step 2: 批量处理住院患者的追溯码补录
            return batchProcessInpatientTraceability(inpatients, weeklyRange);


        } catch (Exception e) {
            log.error("处理住院患者药品追溯码补录异常", e);
            return ApiResult.error("处理失败: " + e.getMessage());
        }
    }


    /**
     * 查询住院患者列表（重命名方法以适应新业务逻辑）
     * 调用HIS接口获取指定时间范围内的住院患者信息
     *
     * @param startDate 开始日期 yyyy-MM-dd
     * @param endDate   结束日期 yyyy-MM-dd
     * @return 住院患者列表
     */
    private List<InpatientSettlementVo> queryInpatientsList(String startDate, String endDate) {
        try {
            log.info("开始查询住院患者列表，时间范围：{} - {}", startDate, endDate);

            // 获取住院患者列表接口URL
            String requestUrl = hangChuangConfig.getInpatientSettlementUrl();

            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executeGetRequestNoTimeout(requestUrl + "?start_time=" + startDate + "&end_time=" + endDate, "查询住院患者列表");

            // 解析响应结果
            List<InpatientSettlementVo> resultList = HttpRequestUtil.parseResponseToList(responseBody, InpatientSettlementVo.class);

            log.info("查询住院患者列表成功，返回{}条数据", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("查询住院患者列表异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据发药药房ID(fyyf)获取对应账号的访问token
     * 从SyncAccountEnum中查找匹配的账号，然后从Redis缓存中获取或重新获取token
     *
     * @param fyyf 发药药房ID
     * @return 访问token字符串，如果获取失败返回null
     */
    private String getTokenByFyyf(String fyyf) {
        try {
            log.info("开始根据发药药房ID获取token，fyyf: {}", fyyf);

            // Step 1: 根据fyyf查找对应的账号枚举
            SyncAccountEnum accountEnum = SyncAccountEnum.findByFyyf(fyyf);
            if (accountEnum == null) {
                log.error("未找到发药药房ID对应的账号配置，fyyf: {}", fyyf);
                return null;
            }

            log.info("找到匹配的账号：{} ({})", accountEnum.getUsername(), accountEnum.getDescription());

            // Step 2: 先尝试从缓存中获取token
            AccessTokenReponse cachedToken = tokenCacheService.getTokenFromCache(accountEnum.getUsername());
            if (cachedToken != null && cachedToken.getReturnCode() == 0 && StrUtil.isNotBlank(cachedToken.getAuthorization())) {
                log.info("从缓存中获取到有效token，账号: {}", accountEnum.getUsername());
                return cachedToken.getAuthorization();
            }

            // Step 3: 缓存中没有或已过期，重新获取token
            log.info("缓存中没有有效token，重新获取，账号: {}", accountEnum.getUsername());
            AccessTokenReponse newToken = SaasHttpUtil.getAccessToken(
                    accountEnum.getUsername(),
                    accountEnum.getPassword()
            );

            if (newToken == null || newToken.getReturnCode() != 0 || StrUtil.isBlank(newToken.getAuthorization())) {
                log.error("获取token失败，账号: {}，响应: {}",
                        accountEnum.getUsername(),
                        newToken != null ? JSONUtil.toJsonStr(newToken) : "null");
                return null;
            }

            // Step 4: 缓存新获取的token
            tokenCacheService.cacheToken(accountEnum.getUsername(), newToken);
            log.info("成功获取并缓存token，账号: {}", accountEnum.getUsername());

            return newToken.getAuthorization();

        } catch (Exception e) {
            log.error("根据发药药房ID获取token异常，fyyf: {}", fyyf, e);
            return null;
        }
    }

    /**
     * 判断费用发生时间是否在指定范围内
     */
    private boolean isWithinDateRange(LocalDateTime feeTime, String startDate, String endDate) {
        if (feeTime == null) return false;

        LocalDate feeDate = feeTime.toLocalDate();
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        return !feeDate.isBefore(start) && !feeDate.isAfter(end);
    }

    /**
     * 查询5204费用明细（兼容方法）
     * 为了保持向后兼容性，保留原方法名
     *
     * @param patient 患者
     * @return {@link List }<{@link Fsi5204Response }>
     * @deprecated 请使用 queryFeeDetailsByDateRange 方法
     */
    @Deprecated
    private List<Fsi5204Response> queryFeeDetails(InpatientSettlementVo patient) {
        try {
            log.info("开始查询患者 {} 的5204费用明细", patient.getPatientName());

            Selinfo5204 selinfo5204 = new Selinfo5204();
            selinfo5204.setPsn_no(patient.getPsnNo());
            selinfo5204.setMdtrt_id(patient.getMdtrtSn());

            NhsaCityResponse<List<Fsi5204Response>> response = nhsaRetryUtil.executeWithRetry(NhsaAccountConstant.getNhsaAccount(),
                    currentSignNo -> NhsaHttpUtil.fsi5204(currentSignNo, selinfo5204, NhsaAccountConstant.getNhsaAccount()));

            return response.getBody().getOutput().stream().filter(item -> "09".equals(item.getMedChrgitmType()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询患者 {} 的5204费用明细异常", patient.getPatientName(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 过滤住院发药数据
     * 通过5204.rx_drord_no与住院发药数据进行关联比对
     *
     * @param feeDetails       5204费用明细列表
     * @param existing3505Data 已存在的3505数据
     * @return 需要处理的未同步数据列表
     */
    private List<InPatientDispenseDetailBindScatteredVo> filterInpatientDispensingData(List<Fsi5204Response> feeDetails,
                                                                                       List<InPatientDispenseDetailBindScatteredVo> existing3505Data) {
        log.info("开始识别需要赋码上传的未同步数据，5204明细{}条，住院处方明细数据{}条",
                feeDetails.size(), existing3505Data.size());

        // 构建5204费用明细的处方号集合
        Set<String> feeDetailRxNos = feeDetails.stream()
                .map(Fsi5204Response::getRxDrordNo)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        List<InPatientDispenseDetailBindScatteredVo> unSyncedData = existing3505Data.stream()
                .filter(data -> feeDetailRxNos.contains(data.getIdFee())) // 在5204中有对应记录
                .collect(Collectors.toList());

        log.info("识别到需要处理的3505未同步数据{}条", unSyncedData.size());
        return unSyncedData;
    }

    /**
     * 识别需要赋码上传的未同步数据
     * 通过5204.rx_drord_no与3505.feedetl_sn进行关联比对
     *
     * @param feeDetails       5204费用明细列表
     * @param existing3505Data 已存在的3505数据
     * @return 需要处理的未同步数据列表
     */
    private List<Nhsa3505> identifyUnSyncedDataWithFeeDetails(List<Fsi5204Response> feeDetails,
                                                              List<Nhsa3505> existing3505Data) {
        log.info("开始识别需要赋码上传的未同步数据，5204明细{}条，3505数据{}条",
                feeDetails.size(), existing3505Data.size());

        // 构建5204费用明细的处方号集合
        Set<String> feeDetailRxNos = feeDetails.stream()
                .map(Fsi5204Response::getRxDrordNo)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 筛选出符合条件的3505数据：
        // 1. hsa_sync_status = '0' (未同步)
        // 2. feedetl_sn 在5204的rx_drord_no中存在
        List<Nhsa3505> unSyncedData = existing3505Data.stream()
                .filter(data -> "0".equals(data.getHsaSyncStatus())) // 未同步
                .filter(data -> feeDetailRxNos.contains(data.getFeedetlSn())) // 在5204中有对应记录
                .collect(Collectors.toList());

        log.info("识别到需要处理的3505未同步数据{}条", unSyncedData.size());
        return unSyncedData;
    }

    /**
     * 批量处理住院患者的追溯码补录
     */
    private ApiResult<String> batchProcessInpatientTraceability(List<InpatientSettlementVo> inpatients,
                                                                DateRange weeklyRange) {
        int successPatients = 0;
        int failedPatients = 0;
        StringBuilder errorMessages = new StringBuilder();
        int totalPatients = inpatients.size();
        log.info("开始批量处理住院患者的追溯码补录，共 {} 位患者", totalPatients);

        for (InpatientSettlementVo patient : inpatients) {
            try {

                log.info("开始处理住院患者：{} (住院号：{})", patient.getPatientName(), patient.getPatInHosId());

                // 处理单个患者
                boolean result = processInpatientWithWeeklyRange(patient, weeklyRange);

                if (result) {
                    successPatients++;
                    log.info("患者 {} 处理成功", patient.getPatientName());
                } else {
                    failedPatients++;
                    log.warn("患者 {} 处理失败", patient.getPatientName());
                    errorMessages.append("患者").append(patient.getPatientName()).append("处理失败; ");
                }

            } catch (Exception e) {
                failedPatients++;
                log.error("处理患者 {} 时发生异常", patient.getPatientName(), e);
                errorMessages.append("患者").append(patient.getPatientName()).append("异常: ").append(e.getMessage())
                        .append("; ");
            }

            // 每处理完一个患者，打印进度信息
            int processedCount = successPatients + failedPatients;
            int remainingCount = totalPatients - processedCount;
            log.info("进度更新: 已处理 {}/{} 位患者，剩余 {} 位患者 (成功: {}，失败: {})",
                    processedCount, totalPatients, remainingCount, successPatients, failedPatients);
        }

        String resultMessage = String.format("处理完成，成功: %d, 失败: %d", successPatients, failedPatients);
        if (!errorMessages.isEmpty()) {
            resultMessage += "，错误详情: " + errorMessages;
        }

        return ApiResult.success(resultMessage);
    }

    /**
     * 处理单个住院患者的追溯码补录（使用周时间范围）
     * 实现完整的业务流程：查询明细、比对差异、补录追溯码、上传数据
     *
     * @param patient     住院患者信息
     * @param weeklyRange 周时间范围
     * @return 处理是否成功
     */
    private boolean processInpatientWithWeeklyRange(InpatientSettlementVo patient,
                                                    DateRange weeklyRange) {
        String patientName = patient.getPatientName();
        try {
            // 查询患者的5204费用明细（使用周时间范围）
            List<Fsi5204Response> feeDetails = queryFeeDetails(patient);
            log.info("患者 {} 查询到5204费用明细 {} 条,", patientName, feeDetails.size());
            feeDetails = feeDetails.stream()
                    .filter(item -> isWithinDateRange(item.getFeeOcurTime(), weeklyRange.getStartDate(), weeklyRange.getEndDate()))
                    .collect(Collectors.toList());
            log.info("患者 {} 在当前统计周期内过滤后的5204费用明细 {} 条", patientName, feeDetails.size());

            if (feeDetails.isEmpty()) {
                log.info("患者 {} 无5204费用明细,跳过处理", patientName);
                return true; // 无数据不算失败
            }

            // Step 1: 查询该患者的住院发药明细
            List<InPatientDispenseDetailBindScatteredVo> patientDispenseDetails = queryPatientDispenseDetails(patient);
            log.info("患者 {} 查询到住院发药明细 {} 条", patientName, patientDispenseDetails.size());

            if (patientDispenseDetails.isEmpty()) {
                log.info("患者 {} 无住院发药明细,跳过处理", patientName);
                return true; // 无数据不算失败
            }

            patientDispenseDetails = filterInpatientDispensingData(feeDetails, patientDispenseDetails);

            // Step 1.5: 将住院发药明细增量保存到3505表
            log.info("患者 {} 开始将住院发药明细增量保存到3505表", patientName);
            try {
                // 使用同步方法保存住院发药数据到3505表
                nhsa3505Service.saveInpatientDataToNhsa3505(patientDispenseDetails);
                log.info("患者 {} 使用同步方法保存住院发药数据到3505表，数据量：{} 条",
                        patientName, patientDispenseDetails.size());
            } catch (Exception e) {
                log.error("患者 {} 保存住院发药明细到3505表时发生异常", patientName, e);
                // 不中断流程，继续后续处理
            }

            // Step 2: 查询已保存的3505业务数据
            List<Nhsa3505> existing3505Data = queryExisting3505Data(patient);
            log.info("患者 {} 已存在3505数据 {} 条", patientName, existing3505Data.size());

            // Step 3: 识别需要赋码上传的未同步数据
            List<Nhsa3505> unSyncedData = identifyUnSyncedDataWithFeeDetails(feeDetails, existing3505Data);

            if (unSyncedData.isEmpty()) {
                log.info("患者 {} 无需要处理的未同步数据", patientName);
                return true;
            }

            // Step 4: 处理追溯码赋值和拆零确认
            boolean traceCodeResult = processTraceCodeAssignment(unSyncedData, patientDispenseDetails, patientName);
            if (!traceCodeResult) {
                log.error("患者 {} 处理追溯码赋值失败", patientName);
                return false;
            }

            // Step 5: 处理未同步数据的赋码上传
            return processUnSyncedDataUpload(unSyncedData, patient);

        } catch (Exception e) {
            log.error("处理患者 {} 时发生异常", patientName, e);
            return false;
        }
    }

    /**
     * 处理追溯码赋值和拆零确认
     * 根据药品明细中的fyyf字段动态获取对应账号的token
     *
     * @param unSyncedData           未同步的3505数据列表
     * @param patientDispenseDetails 患者配药详情
     * @param patientName            患者姓名
     * @return 处理是否成功
     */
    private boolean processTraceCodeAssignment(List<Nhsa3505> unSyncedData, List<InPatientDispenseDetailBindScatteredVo> patientDispenseDetails, String patientName) {
        try {
            log.info("患者 {} 开始处理追溯码赋值和拆零确认，数据量：{}", patientName, unSyncedData.size());

            // Step 1: 数据过滤 - 筛选出需要处理的3505数据
            log.info("患者 {} Step 1: 数据过滤完成，筛选出 {} 条3505数据", patientName, unSyncedData.size());

            // 构建unSyncedData中fixmedinsBchno的集合用于匹配
            Set<String> unSyncedBchnoSet = unSyncedData.stream()
                    .filter(data -> StrUtil.isBlank(data.getDrugTracInfo()))
                    .map(Nhsa3505::getFixmedinsBchno)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 从完整的配药详情中筛选出与unSyncedData匹配的记录
            List<InPatientDispenseDetailBindScatteredVo> filteredDispenseDetails = patientDispenseDetails.stream()
                    .filter(detail -> unSyncedBchnoSet.contains(detail.getFixmedinsBchno()))
                    .collect(Collectors.toList());

            log.info("患者 {} Step 2: 提取患者配药详情完成，全量数据 {} 条，匹配筛选后 {} 条",
                    patientName, patientDispenseDetails.size(), filteredDispenseDetails.size());

            if (filteredDispenseDetails.isEmpty()) {
                log.info("患者 {} 无匹配的配药详情数据", patientName);
                return true;
            }

            // Step 3: 根据fyyf分组处理
            Map<String, List<InPatientDispenseDetailBindScatteredVo>> fyyfGroupMap = filteredDispenseDetails.stream()
                    .filter(detail -> StrUtil.isNotBlank(detail.getFyyf()))
                    .collect(Collectors.groupingBy(InPatientDispenseDetailBindScatteredVo::getFyyf));

            if (fyyfGroupMap.isEmpty()) {
                log.warn("患者 {} 的配药详情中没有有效的fyyf字段", patientName);
                return false;
            }

            log.info("患者 {} Step 3: 根据fyyf分组完成，共 {} 个药房分组", patientName, fyyfGroupMap.size());

            // Step 4: 处理每个fyyf分组
            List<InPatientDispenseDetailBindScatteredVo> allEnrichedDetails = new ArrayList<>();
            int totalAssignedCount = 0;

            for (Map.Entry<String, List<InPatientDispenseDetailBindScatteredVo>> entry : fyyfGroupMap.entrySet()) {
                String fyyf = entry.getKey();
                List<InPatientDispenseDetailBindScatteredVo> groupDetails = entry.getValue();

                log.info("患者 {} 开始处理药房 {} 的数据，数量：{}", patientName, fyyf, groupDetails.size());

                // Step 4.1: 根据fyyf获取对应的token
                String token = getTokenByFyyf(fyyf);
                if (StrUtil.isBlank(token)) {
                    log.error("患者 {} 药房 {} 获取token失败，跳过该分组", patientName, fyyf);
                    continue;
                }

                // Step 4.2: 获取拆零追溯码
                List<InPatientDispenseDetailBindScatteredVo> enrichedGroupDetails =
                        handleTracCodgStoreForInpatient(groupDetails, token);
                log.info("患者 {} 药房 {} Step 4.2: 获取拆零追溯码完成，处理 {} 条数据",
                        patientName, fyyf, enrichedGroupDetails.size());

                // Step 4.3: 追溯码赋值
                int groupAssignedCount = assignTraceCodeToEmptyRecords(unSyncedData, enrichedGroupDetails, patientName);
                log.info("患者 {} 药房 {} Step 4.3: 追溯码赋值完成，更新 {} 条记录",
                        patientName, fyyf, groupAssignedCount);
                totalAssignedCount += groupAssignedCount;

                // Step 4.4: 确认配药数据
                boolean confirmResult = confirmDispenseData(enrichedGroupDetails, token);
                if (!confirmResult) {
                    log.error("患者 {} 药房 {} Step 4.4: 确认配药数据失败", patientName, fyyf);
                    // 继续处理其他分组，不直接返回失败
                } else {
                    log.info("患者 {} 药房 {} Step 4.4: 确认配药数据完成", patientName, fyyf);
                }

                allEnrichedDetails.addAll(enrichedGroupDetails);
            }

            // Step 5: 更新数据库中的3505记录（如果有追溯码更新）
            if (totalAssignedCount > 0) {
                nhsa3505Service.updateBatchById(unSyncedData);
                log.info("患者 {} Step 5: 更新数据库记录完成，总共更新 {} 条记录", patientName, totalAssignedCount);
            }

            log.info("患者 {} 追溯码赋值和拆零确认处理完成，总共处理 {} 个药房分组", patientName, fyyfGroupMap.size());
            return true;

        } catch (Exception e) {
            log.error("患者 {} 处理追溯码赋值和拆零确认时发生异常", patientName, e);
            return false;
        }
    }

    /**
     * 处理未同步数据的赋码上传
     *
     * @param unSyncedData 未同步的3505数据列表
     * @param patient      患者信息
     * @return 处理是否成功
     */
    private boolean processUnSyncedDataUpload(List<Nhsa3505> unSyncedData,
                                              InpatientSettlementVo patient) {
        String patientName = patient.getPatientName();
        try {
            // 过滤掉drugTracInfo追溯码为空的数据
            final List<Nhsa3505> collect = unSyncedData.stream()
                    .filter(item -> StrUtil.isNotBlank(item.getDrugTracInfo()))
                    .collect(Collectors.toList());

            log.info("开始处理患者 {} 的未同步数据上传，数量：{}", patientName, unSyncedData.size());

            // 调用现有的上传逻辑
            ApiResult<String> result = nhsa3505Service.processUpload(collect, false);
            if (result.getCode() != 200) {
                log.error("患者 {} 上传3505数据失败：{}", patientName, result.getMsg());
                return false;
            }

            log.info("患者 {} 的3505数据上传完成", patientName);
            return true;

        } catch (Exception e) {
            log.error("上传患者 {} 的3505数据时发生异常", patientName, e);
            return false;
        }
    }

    /**
     * 查询患者的住院发药明细
     * 根据患者的住院ID查询所有相关的发药记录
     *
     * @param patient 出院患者信息
     * @return 住院发药明细列表
     */
    private List<InPatientDispenseDetailBindScatteredVo> queryPatientDispenseDetails(InpatientSettlementVo patient) {
        try {
            log.info("开始查询患者 {} 的住院发药明细", patient.getPatientName());

            // 构建查询参数 - 使用住院ID作为记录ID查询
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("pat_in_hos_id", patient.getPatInHosId());

            // 获取出院患者列表接口URL
            String requestUrl = hangChuangConfig.getInpatientDispenseDetailUrl();

            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "查询住院发药明细");

            // 解析响应结果
            List<InPatientDispenseDetailBindScatteredVo> resultList = HttpRequestUtil.parseResponseToList(responseBody, InPatientDispenseDetailBindScatteredVo.class);

            log.info("查询住院发药明细成功，返回{}条数据", resultList.size());
            return resultList;


        } catch (Exception e) {
            log.error("查询患者 {} 发药明细his接口异常", patient.getPatientName(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询已存在的3505业务数据
     * 根据患者信息查询已保存在nhsa_3505表中的数据
     *
     * @param patient 出院患者信息
     * @return 已存在的3505数据列表
     */
    private List<Nhsa3505> queryExisting3505Data(InpatientSettlementVo patient) {
        String patientName = patient.getPatientName();
        try {
            log.info("开始查询患者 {} 的已存在3505数据", patientName);

            // 构建查询条件
            LambdaQueryWrapper<Nhsa3505> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Nhsa3505::getMedicalCode, NhsaAccountConstant.getNhsaAccount().getMedicalCode());

            // 根据就医流水号查询（如果有的话）
            if (StrUtil.isNotBlank(patient.getMdtrtSn())) {
                queryWrapper.eq(Nhsa3505::getMdtrtSn, patient.getMdtrtSn());
            } else {
                // 如果没有就医流水号，根据患者姓名查询
                queryWrapper.eq(Nhsa3505::getPatientId, patient.getPatInHosId());
            }

            // 只查询住院数据
            queryWrapper.eq(Nhsa3505::getSdDps, SdDpsEnum.INPATIENT); // 1-住院

            List<Nhsa3505> existingData = nhsa3505Service.list(queryWrapper);
            log.info("患者 {} 查询到已存在3505数据 {} 条", patientName, existingData.size());

            return existingData;

        } catch (Exception e) {
            log.error("查询患者 {} 已存在3505数据异常", patientName, e);
            return new ArrayList<>();
        }
    }


    /**
     * 对drugProdBarc字段为空的记录进行追溯码赋值
     *
     * @param unSyncedData            原始3505数据列表
     * @param enrichedDispenseDetails 包含追溯码信息的配药详情列表
     * @param patientName             患者姓名
     * @return 实际更新的记录数量
     */
    private int assignTraceCodeToEmptyRecords(List<Nhsa3505> unSyncedData,
                                              List<InPatientDispenseDetailBindScatteredVo> enrichedDispenseDetails,
                                              String patientName) {
        int assignedCount = 0;

        // 创建映射关系，以fixmedinsBchno为key
        Map<String, InPatientDispenseDetailBindScatteredVo> enrichedMap = enrichedDispenseDetails.stream()
                .filter(detail -> StringUtils.isNotEmpty(detail.getFixmedinsBchno()))
                .collect(Collectors.toMap(
                        InPatientDispenseDetailBindScatteredVo::getFixmedinsBchno,
                        Function.identity(),
                        (existing, replacement) -> replacement // 如果有重复key，使用新值
                ));

        for (Nhsa3505 nhsa3505 : unSyncedData) {
            try {
                // 只对drugProdBarc字段为空（null或空字符串）的记录进行处理
                String fixmedinsBchno = nhsa3505.getFixmedinsBchno();
                if (StringUtils.isNotEmpty(fixmedinsBchno) && enrichedMap.containsKey(fixmedinsBchno)) {
                    InPatientDispenseDetailBindScatteredVo enrichedDetail = enrichedMap.get(fixmedinsBchno);

                    // 赋值追溯码信息
                    if (enrichedDetail.getDrugTracCodgs() != null && !enrichedDetail.getDrugTracCodgs().isEmpty()) {
                        nhsa3505.setDrugTracInfo(String.join(",", enrichedDetail.getDrugTracCodgs()));
                        nhsa3505.setTrdnFlag(enrichedDetail.getTrdnFlag());
                        nhsa3505.setInvCnt(enrichedDetail.getCurrNum());
                        nhsa3505.setRemark("当日有明细自动赋码上传");
                        assignedCount++;
                        log.debug("患者 {} 为记录 {} (fixmedinsBchno: {}) 赋值追溯码：{}",
                                patientName, nhsa3505.getId(), fixmedinsBchno, enrichedDetail.getDrugCode());
                    } else {
                        nhsa3505.setInvCnt(BigDecimal.ZERO);
                        nhsa3505.setRemark("没有赋码");
                    }
                }
            } catch (Exception e) {
                log.error("患者 {} 为记录 {} 赋值追溯码时发生异常", patientName, nhsa3505.getId(), e);
            }
        }

        log.info("患者 {} 追溯码赋值完成，共处理 {} 条记录，实际赋值 {} 条", patientName, unSyncedData.size(), assignedCount);
        return assignedCount;
    }


    /**
     * 确认拆零信息
     * 调用SaaS接口确认发药数据
     *
     * @param processedDataList 已处理的数据列表
     * @param token             访问token
     * @return 确认是否成功
     */
    private boolean confirmDispenseData(List<InPatientDispenseDetailBindScatteredVo> processedDataList, String token) {
        try {
            log.info("开始确认拆零信息，数量：{}", processedDataList.size());

            // 过滤出需要确认的拆零药品
            List<InPatientDispenseDetailBindScatteredVo> confirmList = processedDataList.stream()
                    .filter(item -> "1".equals(item.getTrdnFlag()))
                    .collect(Collectors.toList());

            if (confirmList.isEmpty()) {
                log.info("无需要确认的拆零药品");
                return true;
            }

            // 构建确认发药接口请求参数
            List<ConfirmDispDrugDataRequest> requestDataList = new ArrayList<>();
            for (InPatientDispenseDetailBindScatteredVo record : confirmList) {
                ConfirmDispDrugDataRequest request = new ConfirmDispDrugDataRequest();
                request.setDrugCode(record.getHisDrugCode());
                request.setDispCnt(record.getSelRetnCnt());
                request.setCfxh(record.getCfxh());
                request.setCfmxxh(record.getCfmxxh());
                requestDataList.add(request);
            }

            if (!requestDataList.isEmpty()) {
                // 构建批次请求
                ConfirmDispDrugRequest confirmRequest = new ConfirmDispDrugRequest();
                confirmRequest.setDataList(requestDataList);

                // 调用confirmDispDrug接口
                SaasHttpUtil.confirmDispDrug(token, confirmRequest);
                log.info("确认拆零信息成功，确认数量：{}", requestDataList.size());
            }

            return true;

        } catch (Exception e) {
            log.error("确认拆零信息时发生异常", e);
            return false;
        }
    }
}
