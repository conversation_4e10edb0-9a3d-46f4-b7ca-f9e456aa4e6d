package com.zsm.model.vo;

import com.zsm.model.saas.response.GetTracCodgStoreDataResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 添加拆零信息的住院发药明细信息VO
 *
 * <AUTHOR>
 * @date 2025/6/11 16:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InPatientDispenseDetailBindScatteredVo extends InPatientDispenseDetailVo {
    // saas拆零接口字段
    /**
     * 药品编码
     */
    private String drugCode;
    /**
     * 药品追溯码
     */
    private List<String> drugTracCodgs;
    /**
     * 发药数量
     */
    private String dispCnt;
    /**
     * 当前库存数量
     */
    private BigDecimal currNum;
    /**
     * 追溯码存储信息
     */
    private GetTracCodgStoreDataResponse tracCodgStore;


    /**
     * 任务明细ID
     */
    @Schema(description = "任务明细ID")
    private String taskIdSubDps;

    /**
     * 已扫描追溯码
     */
    @Schema(description = "已扫描追溯码")
    private String taskDrugtracinfoDps;

    /**
     * 明细扫描时间
     */
    @Schema(description = "明细扫描时间")
    private String taskDetailScanTimeDps;

    /**
     * 明细扫描状态
     */
    @Schema(description = "明细扫描状态")
    private String taskFgScannedDps;

    /**
     * 扫描状态 (0-未扫码, 1-已扫码)
     */
    @Schema(description = "扫描状态 (0-未扫码, 1-已扫码)")
    private String scanStatus;

    /**
     * 最后扫描时间
     */
    @Schema(description = "最后扫描时间")
    private String lastScanTime;

    /**
     * 药品追溯码信息
     */
    @Schema(description = "药品追溯码信息")
    private String drugTracInfo;

}
